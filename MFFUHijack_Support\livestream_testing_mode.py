"""
Live Stream Testing Mode for MFFUHijack
Allows testing on previous livestreams with configurable intervals and code validation
"""

import os
import sys
import time
import cv2
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
import yt_dlp
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class CodeValidationTester:
    """Handles code validation testing without purchasing"""
    
    def __init__(self):
        self.driver = None
        self.validation_results = []
        self.browser_setup_attempted = False
        # Don't setup browser immediately to avoid network errors
    
    def setup_browser(self):
        """Setup headless browser for code validation"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ Browser setup completed for code validation")
            
        except Exception as e:
            print(f"❌ Browser setup failed: {e}")
            self.driver = None
    
    def validate_code(self, code: str, account_type: str) -> Dict:
        """Validate a code without completing purchase"""
        # Setup browser on first use to avoid network errors during initialization
        if not self.driver and not self.browser_setup_attempted:
            self.setup_browser()
            self.browser_setup_attempted = True

        if not self.driver:
            return {"valid": False, "error": "Browser not available"}
        
        try:
            # Navigate to appropriate checkout page based on account type
            checkout_urls = {
                "Reset": "https://myfundedfutures.com/checkout/reset-account",
                "Starter": "https://myfundedfutures.com/checkout/starter-account", 
                "Starter Plus": "https://myfundedfutures.com/checkout/starter-plus-account",
                "Expert": "https://myfundedfutures.com/checkout/expert-account",
                "Free Reset Code": "https://myfundedfutures.com/checkout/reset-account"
            }
            
            url = checkout_urls.get(account_type, checkout_urls["Starter"])
            self.driver.get(url)
            
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Find and fill the discount code field
            code_field_selectors = [
                "input[name='discount_code']",
                "input[placeholder*='code']",
                "input[placeholder*='Code']",
                "input[placeholder*='discount']",
                "input[placeholder*='Discount']",
                "#discount_code",
                ".discount-code",
                "input[type='text'][name*='code']"
            ]
            
            code_field = None
            for selector in code_field_selectors:
                try:
                    code_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            if not code_field:
                return {"valid": False, "error": "Code field not found"}
            
            # Clear and enter the code
            code_field.clear()
            code_field.send_keys(code)
            
            # Find and click apply button
            apply_button_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "button:contains('Apply')",
                "button:contains('APPLY')",
                ".apply-button",
                "#apply-discount",
                "button[name*='apply']"
            ]
            
            apply_button = None
            for selector in apply_button_selectors:
                try:
                    if ":contains(" in selector:
                        # Handle text-based selectors
                        text = selector.split("'")[1]
                        apply_button = self.driver.find_element(
                            By.XPATH, f"//button[contains(text(), '{text}')]"
                        )
                    else:
                        apply_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            if not apply_button:
                return {"valid": False, "error": "Apply button not found"}
            
            # Click apply button
            apply_button.click()
            
            # Wait for response and check for validation
            time.sleep(2)
            
            # Check for success/error messages
            success_indicators = [
                "discount applied",
                "code applied",
                "savings",
                "discount",
                "success",
                "valid"
            ]
            
            error_indicators = [
                "invalid",
                "expired",
                "not found",
                "error",
                "failed",
                "incorrect"
            ]
            
            page_text = self.driver.page_source.lower()
            
            # Check for success indicators
            for indicator in success_indicators:
                if indicator in page_text:
                    result = {
                        "valid": True,
                        "code": code,
                        "account_type": account_type,
                        "message": f"Code appears valid - found '{indicator}' in response",
                        "timestamp": datetime.now().isoformat()
                    }
                    self.validation_results.append(result)
                    return result
            
            # Check for error indicators
            for indicator in error_indicators:
                if indicator in page_text:
                    result = {
                        "valid": False,
                        "code": code,
                        "account_type": account_type,
                        "message": f"Code appears invalid - found '{indicator}' in response",
                        "timestamp": datetime.now().isoformat()
                    }
                    self.validation_results.append(result)
                    return result
            
            # If no clear indicators, assume invalid
            result = {
                "valid": False,
                "code": code,
                "account_type": account_type,
                "message": "No clear validation response detected",
                "timestamp": datetime.now().isoformat()
            }
            self.validation_results.append(result)
            return result
            
        except TimeoutException:
            return {"valid": False, "error": "Page load timeout"}
        except Exception as e:
            return {"valid": False, "error": f"Validation error: {str(e)}"}
    
    def get_validation_results(self) -> List[Dict]:
        """Get all validation results"""
        return self.validation_results
    
    def close(self):
        """Close the browser"""
        if self.driver:
            self.driver.quit()
            self.driver = None


class StreamProcessor:
    """Processes video streams with configurable frame intervals"""
    
    def __init__(self):
        self.cap = None
        self.total_frames = 0
        self.fps = 30
        self.duration = 0
        self.current_frame = 0
        
    def load_stream(self, stream_path: str) -> bool:
        """Load a video stream for processing"""
        try:
            self.cap = cv2.VideoCapture(stream_path)
            
            if not self.cap.isOpened():
                return False
            
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            self.duration = self.total_frames / self.fps if self.fps > 0 else 0
            self.current_frame = 0
            
            print(f"✅ Stream loaded: {self.total_frames} frames, {self.fps:.1f} FPS, {self.duration:.1f}s duration")
            return True
            
        except Exception as e:
            print(f"❌ Failed to load stream: {e}")
            return False
    
    def get_frame_at_interval(self, interval_seconds: float) -> Optional[Tuple[bool, any]]:
        """Get next frame based on interval"""
        if not self.cap:
            return None
        
        # Calculate frame number based on interval
        frames_to_skip = int(interval_seconds * self.fps)
        target_frame = self.current_frame + frames_to_skip
        
        if target_frame >= self.total_frames:
            return None  # End of stream
        
        # Seek to target frame
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, target_frame)
        ret, frame = self.cap.read()
        
        if ret:
            self.current_frame = target_frame
            return ret, frame
        
        return None
    
    def get_current_timestamp(self) -> str:
        """Get current timestamp in the video"""
        if not self.cap:
            return "00:00:00"
        
        current_time = self.current_frame / self.fps if self.fps > 0 else 0
        hours = int(current_time // 3600)
        minutes = int((current_time % 3600) // 60)
        seconds = int(current_time % 60)
        
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    
    def get_progress_percentage(self) -> float:
        """Get processing progress as percentage"""
        if self.total_frames == 0:
            return 0.0
        return (self.current_frame / self.total_frames) * 100
    
    def close(self):
        """Close the video capture"""
        if self.cap:
            self.cap.release()
            self.cap = None


class YouTubeStreamDownloader:
    """Downloads YouTube streams for testing"""
    
    def __init__(self):
        self.download_path = "test_streams"
        self.ensure_download_directory()
    
    def ensure_download_directory(self):
        """Ensure download directory exists"""
        if not os.path.exists(self.download_path):
            os.makedirs(self.download_path)
    
    def get_stream_info(self, url: str) -> Dict:
        """Get information about a YouTube stream"""
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                return {
                    'title': info.get('title', 'Unknown'),
                    'duration': info.get('duration', 0),
                    'uploader': info.get('uploader', 'Unknown'),
                    'upload_date': info.get('upload_date', 'Unknown'),
                    'view_count': info.get('view_count', 0),
                    'formats': len(info.get('formats', [])),
                    'is_live': info.get('is_live', False),
                    'was_live': info.get('was_live', False)
                }
                
        except Exception as e:
            return {'error': str(e)}
    
    def download_stream(self, url: str, quality: str = 'best') -> str:
        """Download a YouTube stream for testing"""
        try:
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_template = os.path.join(self.download_path, f"test_stream_{timestamp}.%(ext)s")
            
            ydl_opts = {
                'format': f'{quality}[ext=mp4]/best[ext=mp4]/best',
                'outtmpl': output_template,
                'quiet': False,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([url])
                
                # Find the downloaded file
                for file in os.listdir(self.download_path):
                    if file.startswith(f"test_stream_{timestamp}"):
                        return os.path.join(self.download_path, file)
                
                return None
                
        except Exception as e:
            print(f"❌ Download failed: {e}")
            return None
    
    def list_downloaded_streams(self) -> List[Dict]:
        """List all downloaded streams"""
        streams = []
        
        if not os.path.exists(self.download_path):
            return streams
        
        for file in os.listdir(self.download_path):
            if file.endswith(('.mp4', '.mkv', '.webm')):
                file_path = os.path.join(self.download_path, file)
                file_stats = os.stat(file_path)
                
                streams.append({
                    'filename': file,
                    'path': file_path,
                    'size': file_stats.st_size,
                    'modified': datetime.fromtimestamp(file_stats.st_mtime),
                    'size_mb': file_stats.st_size / (1024 * 1024)
                })
        
        return sorted(streams, key=lambda x: x['modified'], reverse=True)


class TestingStatistics:
    """Tracks testing session statistics"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """Reset all statistics"""
        self.session_start = datetime.now()
        self.frames_processed = 0
        self.codes_detected = 0
        self.codes_validated = 0
        self.valid_codes = 0
        self.invalid_codes = 0
        self.validation_errors = 0
        self.processing_times = []
        self.detection_events = []
        self.validation_results = []
    
    def add_frame_processed(self, processing_time: float):
        """Add a processed frame"""
        self.frames_processed += 1
        self.processing_times.append(processing_time)
    
    def add_code_detected(self, code_data: Dict):
        """Add a detected code"""
        self.codes_detected += 1
        self.detection_events.append({
            'timestamp': datetime.now(),
            'code': code_data.get('code', ''),
            'type': code_data.get('type', ''),
            'confidence': code_data.get('confidence', 0)
        })
    
    def add_validation_result(self, result: Dict):
        """Add a validation result"""
        self.codes_validated += 1
        self.validation_results.append(result)
        
        if result.get('valid', False):
            self.valid_codes += 1
        elif 'error' in result:
            self.validation_errors += 1
        else:
            self.invalid_codes += 1
    
    def get_session_duration(self) -> timedelta:
        """Get session duration"""
        return datetime.now() - self.session_start
    
    def get_average_processing_time(self) -> float:
        """Get average frame processing time"""
        if not self.processing_times:
            return 0.0
        return sum(self.processing_times) / len(self.processing_times)
    
    def get_detection_rate(self) -> float:
        """Get codes detected per frame"""
        if self.frames_processed == 0:
            return 0.0
        return self.codes_detected / self.frames_processed
    
    def get_validation_success_rate(self) -> float:
        """Get validation success rate"""
        if self.codes_validated == 0:
            return 0.0
        return (self.valid_codes / self.codes_validated) * 100
    
    def get_comprehensive_stats(self) -> Dict:
        """Get comprehensive statistics"""
        duration = self.get_session_duration()
        
        return {
            'session_duration': str(duration).split('.')[0],
            'frames_processed': self.frames_processed,
            'codes_detected': self.codes_detected,
            'codes_validated': self.codes_validated,
            'valid_codes': self.valid_codes,
            'invalid_codes': self.invalid_codes,
            'validation_errors': self.validation_errors,
            'average_processing_time': self.get_average_processing_time(),
            'detection_rate': self.get_detection_rate(),
            'validation_success_rate': self.get_validation_success_rate(),
            'frames_per_minute': (self.frames_processed / duration.total_seconds() * 60) if duration.total_seconds() > 0 else 0
        }
