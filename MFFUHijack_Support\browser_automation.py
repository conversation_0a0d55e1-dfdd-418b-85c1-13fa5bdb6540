"""
Browser Automation Module for MFFUHijack
Handles browser automation for My Funded Futures website interactions
"""

import os
import sys
import time
import logging
from typing import Optional, Dict, List, Tuple, Any
import threading
import traceback

# Setup logging - only show errors from selenium
logging.basicConfig(level=logging.ERROR)
logger = logging.getLogger(__name__)

# Disable selenium logging except for errors
selenium_logger = logging.getLogger('selenium')
selenium_logger.setLevel(logging.ERROR)
urllib3_logger = logging.getLogger('urllib3')
urllib3_logger.setLevel(logging.ERROR)

class ConsoleLogger:
    """Clean console logger with colors and ASCII art"""

    # ANSI color codes
    RED = '\033[91m'
    YELLOW = '\033[93m'
    GREEN = '\033[92m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    RESET = '\033[0m'

    @staticmethod
    def print_banner():
        """Print MFFUHIJACK ASCII banner"""
        banner = """
 ███╗   ███╗███████╗███████╗██╗   ██╗██╗  ██╗██╗     ██╗ █████╗  ██████╗██╗  ██╗
 ████╗ ████║██╔════╝██╔════╝██║   ██║██║  ██║██║     ██║██╔══██╗██╔════╝██║ ██╔╝
 ██╔████╔██║█████╗  █████╗  ██║   ██║███████║██║     ██║███████║██║     █████╔╝
 ██║╚██╔╝██║██╔══╝  ██╔══╝  ██║   ██║██╔══██║██║██   ██║██╔══██║██║     ██╔═██╗
 ██║ ╚═╝ ██║██║     ██║     ╚██████╔╝██║  ██║██║╚█████╔╝██║  ██║╚██████╗██║  ██╗
 ╚═╝     ╚═╝╚═╝     ╚═╝      ╚═════╝ ╚═╝  ╚═╝╚═╝ ╚════╝ ╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝

                        My Funded Futures Code Hijacker v1.0
                              Real-time OCR & Automation
        """
        print(ConsoleLogger.CYAN + banner + ConsoleLogger.RESET)

    @staticmethod
    def error(message):
        """Print error message with red [+]"""
        print(f"{ConsoleLogger.RED}[+]{ConsoleLogger.RESET} {message}")

    @staticmethod
    def warning(message):
        """Print warning message with yellow [+]"""
        print(f"{ConsoleLogger.YELLOW}[+]{ConsoleLogger.RESET} {message}")

    @staticmethod
    def success(message):
        """Print success message with green [+]"""
        print(f"{ConsoleLogger.GREEN}[+]{ConsoleLogger.RESET} {message}")

    @staticmethod
    def info(message):
        """Print info message with white [+]"""
        print(f"{ConsoleLogger.WHITE}[+]{ConsoleLogger.RESET} {message}")

    @staticmethod
    def code_found(message):
        """Print code found message with green [+]"""
        print(f"{ConsoleLogger.GREEN}[+]{ConsoleLogger.RESET} {message}")

# Global console logger instance
console = ConsoleLogger()

def initialize_console():
    """Initialize console with banner - call once at startup"""
    console.print_banner()
    console.info("System initialized and ready")
    console.info("Waiting for browser automation commands...")

# Check if selenium is installed, if not provide instructions
try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.common.keys import Keys
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.common.exceptions import (
        TimeoutException, NoSuchElementException,
        ElementNotInteractableException, WebDriverException
    )
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.chrome.options import Options
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    logger.error("Selenium not installed. Browser automation will not be available.")

# PyQt signal handling
try:
    from PyQt6.QtCore import QObject, pyqtSignal
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False
    logger.error("PyQt6 not installed. GUI integration will not be available.")

# URLs for My Funded Futures
MFF_URLS = {
    "login": "https://myfundedfutures.com/login",
    "stats": "https://myfundedfutures.com/stats",
    "starter_plus": "https://myfundedfutures.com/challenge?id=39&platform=Tradovate",
    "starter": "https://myfundedfutures.com/challenge?id=40&platform=Tradovate",
    "expert": "https://myfundedfutures.com/challenge?id=43&platform=Tradovate",
    "free_reset_code": "https://myfundedfutures.com/stats"  # Reset codes use stats page
}

class BrowserAutomationSignals(QObject):
    """Signals for browser automation events"""
    if PYQT_AVAILABLE:
        status_update = pyqtSignal(str)
        error_occurred = pyqtSignal(str)
        browser_ready = pyqtSignal()
        login_ready = pyqtSignal()
        code_submitted = pyqtSignal(bool, str)  # success, message

class BrowserAutomation:
    """Handles browser automation for My Funded Futures website"""
    
    def __init__(self):
        self.driver = None
        self.is_initialized = False
        self.is_visible = True
        self.account_type = None
        self.signals = BrowserAutomationSignals() if PYQT_AVAILABLE else None
        self.preloaded_pages = {}
        self.current_tab = None
        self.setup_cvv = ""  # Store CVV for pre-filling during setup
        
    def log_message(self, message: str, level: str = "info"):
        """Log clean, readable message - only log errors to console"""
        if level == "info":
            # Only log to GUI, not console
            if self.signals and PYQT_AVAILABLE:
                self.signals.status_update.emit(message)
        elif level == "error":
            # Log errors to both console and GUI
            logger.error(f"ERROR: {message}")
            console.error(message)
            if self.signals and PYQT_AVAILABLE:
                self.signals.error_occurred.emit(f"ERROR: {message}")
        elif level == "warning":
            # Log warnings to both console and GUI
            logger.warning(message)
            console.warning(message)
            if self.signals and PYQT_AVAILABLE:
                self.signals.status_update.emit(f"WARNING: {message}")
        elif level == "success":
            # Log success to both console and GUI
            console.success(message)
            if self.signals and PYQT_AVAILABLE:
                self.signals.status_update.emit(message)
        elif level == "debug":
            # Debug only goes to logger, not console
            logger.debug(message)
        
    def initialize_browser(self) -> bool:
        """Initialize the browser with optimized settings for CAPTCHA and images"""
        if not SELENIUM_AVAILABLE:
            self.log_message("❌ Selenium not installed. Please install with: pip install selenium", "error")
            return False

        try:
            self.log_message("🌐 Initializing clean default browser...")

            # Set up basic Chrome options - clean default browser
            chrome_options = Options()
            if not self.is_visible:
                chrome_options.add_argument("--headless")

            # Set window size to 1280x720
            chrome_options.add_argument("--window-size=1280,720")

            # Initialize Chrome driver
            self.driver = webdriver.Chrome(options=chrome_options)

            # Set window size after initialization to ensure it's applied
            self.driver.set_window_size(1280, 720)

            self.is_initialized = True
            self.log_message("✅ Clean default browser ready (1280x720)")

            if self.signals and PYQT_AVAILABLE:
                self.signals.browser_ready.emit()

            return True
        except Exception as e:
            self.log_message(f"❌ Failed to initialize browser: {str(e)}", "error")
            return False
            
    def set_visibility(self, visible: bool):
        """Set browser visibility with CAPTCHA optimization"""
        self.is_visible = visible
        self.log_message(f"🔍 Browser visibility set to: {visible}")

        # If browser is already running, we need to restart it
        if self.is_initialized:
            self.close_browser()
            self.initialize_browser()

        # If making visible for CAPTCHA, optimize window
        if visible and self.is_initialized:
            self._optimize_for_captcha()

    def _optimize_for_captcha(self):
        """Optimize browser window for CAPTCHA interaction"""
        try:
            # Maximize window for better CAPTCHA visibility
            self.driver.maximize_window()

            # Ensure window is focused
            self.driver.switch_to.window(self.driver.current_window_handle)

            # Inject CSS to improve CAPTCHA visibility
            self.driver.execute_script("""
                // Improve CAPTCHA visibility
                var style = document.createElement('style');
                style.innerHTML = `
                    .captcha, .g-recaptcha, .h-captcha, .cf-turnstile {
                        z-index: 999999 !important;
                        position: relative !important;
                        visibility: visible !important;
                        display: block !important;
                    }

                    iframe[src*="recaptcha"], iframe[src*="hcaptcha"], iframe[src*="turnstile"] {
                        z-index: 999999 !important;
                        visibility: visible !important;
                        display: block !important;
                    }

                    /* Ensure CAPTCHA containers are visible */
                    div:has(.g-recaptcha), div:has(.h-captcha), div:has(.cf-turnstile) {
                        z-index: 999998 !important;
                        visibility: visible !important;
                        display: block !important;
                    }
                `;
                document.head.appendChild(style);

                // Force repaint
                document.body.style.display = 'none';
                document.body.offsetHeight;
                document.body.style.display = '';
            """)

            self.log_message("✅ Browser optimized for CAPTCHA interaction")

        except Exception as e:
            self.log_message(f"⚠️ CAPTCHA optimization failed: {str(e)}")

    def close_browser(self):
        """Close the browser and cleanup"""
        if self.driver:
            try:
                self.driver.quit()
                self.log_message("🔒 Browser closed")
            except Exception as e:
                self.log_message(f"⚠️ Error closing browser: {str(e)}", "error")
        self.driver = None
        self.is_initialized = False
        self.preloaded_pages = {}



    def login_to_mff(self, username: str, password: str) -> bool:
        """Login to My Funded Futures website"""
        if not self.is_initialized:
            if not self.initialize_browser():
                return False

        try:
            self.log_message("🔐 Navigating to login page...")

            # Navigate with timeout protection
            try:
                self.driver.get(MFF_URLS["login"])
            except Exception as e:
                self.log_message(f"⚠️ Page load timeout, but continuing: {str(e)}")

            # Wait for page to load with shorter timeout
            try:
                WebDriverWait(self.driver, 8).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
            except TimeoutException:
                self.log_message("⚠️ Page load timeout, but body found - continuing")



            self.log_message("📝 Filling login credentials...")

            # Enhanced username field detection with multiple selectors
            username_selectors = [
                "//input[@placeholder='Username']",
                "//input[@name='username']",
                "//input[@id='username']",
                "//input[@type='text']",
                "//input[contains(@class, 'username')]",
                "//input[contains(@placeholder, 'user')]"
            ]

            username_field = None
            for selector in username_selectors:
                try:
                    username_field = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    self.log_message(f"✅ Found username field with selector: {selector}")
                    break
                except (TimeoutException, NoSuchElementException):
                    continue

            if not username_field:
                self.log_message("❌ Could not find username field", "error")
                return False

            try:
                # Clear and fill username with enhanced method
                username_field.clear()
                time.sleep(0.2)  # Brief pause
                username_field.send_keys(username)
                self.log_message(f"✅ Username entered: {username}")
            except Exception as e:
                self.log_message(f"❌ Error entering username: {str(e)}", "error")
                return False

            # Enhanced password field detection with multiple selectors
            password_selectors = [
                "//input[@placeholder='Password']",
                "//input[@name='password']",
                "//input[@id='password']",
                "//input[@type='password']",
                "//input[contains(@class, 'password')]",
                "//input[contains(@placeholder, 'pass')]"
            ]

            password_field = None
            for selector in password_selectors:
                try:
                    password_field = WebDriverWait(self.driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    self.log_message(f"✅ Found password field with selector: {selector}")
                    break
                except (TimeoutException, NoSuchElementException):
                    continue

            if not password_field:
                self.log_message("❌ Could not find password field", "error")
                return False

            try:
                # Clear and fill password with enhanced method
                password_field.clear()
                time.sleep(0.2)  # Brief pause
                password_field.send_keys(password)
                self.log_message("✅ Password entered")
            except Exception as e:
                self.log_message(f"❌ Error entering password: {str(e)}", "error")
                return False

            # Enhanced CAPTCHA detection with multiple selectors
            captcha_detected = False
            captcha_selectors = [
                "captcha",  # Class name
                "g-recaptcha",  # Google reCAPTCHA
                "h-captcha",  # hCaptcha
                "cf-turnstile",  # Cloudflare Turnstile
                "recaptcha",  # Alternative reCAPTCHA
                "[data-sitekey]",  # Any element with sitekey (CAPTCHA indicator)
                "iframe[src*='recaptcha']",  # reCAPTCHA iframe
                "iframe[src*='hcaptcha']",  # hCaptcha iframe
                "iframe[src*='turnstile']",  # Turnstile iframe
            ]

            for selector in captcha_selectors:
                try:
                    if selector.startswith("[") or "iframe" in selector:
                        # CSS selector
                        captcha_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    else:
                        # Class name
                        captcha_element = self.driver.find_element(By.CLASS_NAME, selector)

                    if captcha_element.is_displayed():
                        captcha_detected = True
                        self.log_message(f"🤖 CAPTCHA detected ({selector}) - Please complete manually")
                        break
                except NoSuchElementException:
                    continue

            if captcha_detected:
                self.log_message("🔍 CAPTCHA found - Browser will remain visible for manual completion")
                # Ensure browser is visible for CAPTCHA completion
                if not self.is_visible:
                    self.set_visibility(True)

                if self.signals and PYQT_AVAILABLE:
                    self.signals.login_ready.emit()
                return True
            else:
                self.log_message("ℹ️ No CAPTCHA detected - proceeding with automatic login")

            self.log_message("⏳ Ready for manual Sign In - Please complete CAPTCHA (if present) and click Sign In")
            if self.signals and PYQT_AVAILABLE:
                self.signals.login_ready.emit()
            return True

        except TimeoutException as e:
            self.log_message(f"⏰ Login timeout: {str(e)}", "warning")
            self.log_message("💡 Please complete login manually in the browser")
            return True  # Return True to allow manual completion
        except NoSuchElementException as e:
            self.log_message(f"❌ Login form elements not found: {str(e)}", "error")
            return False
        except Exception as e:
            self.log_message(f"❌ Login failed: {str(e)}", "error")
            # Only log full traceback in debug mode
            if hasattr(self, 'debug_mode') and self.debug_mode:
                self.log_message(traceback.format_exc(), "debug")
            return False

    def wait_for_login_completion(self, timeout: int = 60) -> bool:
        """Wait for user to complete login manually with enhanced feedback and network protection"""
        try:
            self.log_message("⏳ Waiting for login completion...")
            self.log_message("🔍 Please complete CAPTCHA if present and click Sign In")



            start_time = time.time()
            check_interval = 2  # Check every 2 seconds

            while time.time() - start_time < timeout:
                try:
                    current_url = self.driver.current_url.lower()

                    # Check if we've moved away from login page
                    if "login" not in current_url and "sign" not in current_url:
                        self.log_message("✅ Login completed successfully - redirected to dashboard")
                        return True

                    # Check for successful login indicators
                    success_indicators = [
                        "dashboard", "account", "profile", "stats", "challenge"
                    ]

                    if any(indicator in current_url for indicator in success_indicators):
                        self.log_message("✅ Login completed - detected success page")
                        return True

                except Exception as e:
                    # Suppress common Selenium errors during waiting
                    if "no such window" not in str(e).lower() and "invalid session" not in str(e).lower():
                        self.log_message(f"⚠️ Error checking URL (continuing): {str(e)}")
                    # Continue waiting even if URL check fails

                # Check for login errors
                try:
                    error_selectors = [
                        "//div[contains(@class, 'error')]",
                        "//div[contains(@class, 'alert-danger')]",
                        "//span[contains(@class, 'error')]",
                        "//div[contains(text(), 'Invalid') or contains(text(), 'incorrect')]"
                    ]

                    for selector in error_selectors:
                        try:
                            error_element = self.driver.find_element(By.XPATH, selector)
                            if error_element.is_displayed():
                                error_text = error_element.text
                                self.log_message(f"❌ Login error detected: {error_text}")
                                return False
                        except NoSuchElementException:
                            continue

                except Exception:
                    pass



                # Provide periodic feedback
                elapsed = int(time.time() - start_time)
                if elapsed % 10 == 0 and elapsed > 0:
                    remaining = timeout - elapsed
                    self.log_message(f"⏳ Still waiting for login... ({remaining}s remaining)")
                    self.log_message("💡 If page seems stuck, try refreshing or completing CAPTCHA")

                time.sleep(check_interval)

            self.log_message("⏰ Login timeout - Please complete login manually", "error")
            self.log_message("💡 Make sure to complete CAPTCHA and click Sign In button")
            return False

        except Exception as e:
            self.log_message(f"❌ Error waiting for login: {str(e)}", "error")
            return False

    def open_manual_tabs(self, account_types: List[str], cvv: str = "") -> bool:
        """Open tabs and automate standard accounts, manual setup for reset accounts"""
        if not self.is_initialized:
            self.log_message("Browser not initialized", "error")
            return False

        if not account_types:
            self.log_message("No account types selected", "error")
            return False

        try:
            self.log_message(f"Opening {len(account_types)} tabs...")
            self.log_message(f"Selected accounts: {', '.join(account_types)}")

            # Store CVV for use during setup
            self.setup_cvv = cvv
            if cvv:
                self.log_message("💳 CVV provided - will pre-fill during setup")

            # Clear any existing preloaded pages
            self.preloaded_pages = {}

            # Separate standard and reset account types
            standard_types = []
            reset_types = []

            for account_type in account_types:
                if account_type.lower() in ["free reset code", "reset"]:
                    reset_types.append(account_type)
                else:
                    standard_types.append(account_type)

            # Process ALL account types - each gets its own tab
            for i, account_type in enumerate(account_types):
                url = self._get_account_url(account_type)
                if not url:
                    self.log_message(f"Unknown account type: {account_type}", "error")
                    continue

                self.log_message(f"Tab {i+1}: Loading {account_type}")

                try:
                    if i == 0:
                        # First tab - use current window
                        self.driver.get(url)
                        current_tab = self.driver.current_window_handle
                        # Set tab title for first tab
                        self.driver.execute_script(f"document.title = 'MFFUHijack - {account_type}';")
                    else:
                        # Additional tabs - open new tab
                        self.driver.execute_script("window.open('');")
                        tabs = self.driver.window_handles
                        current_tab = tabs[-1]

                        # Switch to new tab and navigate
                        self.driver.switch_to.window(current_tab)
                        self.driver.get(url)
                        # Set tab title for new tab
                        self.driver.execute_script(f"document.title = 'MFFUHijack - {account_type}';")

                    self.log_message(f"Tab titled: MFFUHijack - {account_type}")

                    # Automate if it's a standard account type - optimized for speed
                    if account_type in standard_types:
                        if self._navigate_to_checkout():
                            self.preloaded_pages[account_type] = current_tab
                            console.success(f"{account_type} automated successfully")
                            # Skip tab title updates to avoid delays
                        else:
                            console.error(f"Failed to automate {account_type}")
                            # Skip error tab title updates to avoid delays
                    else:
                        # Reset account - just open the tab
                        self.preloaded_pages[account_type] = current_tab
                        console.info(f"{account_type} tab opened for manual setup")
                        self.log_message(f"Reset account {account_type} tab opened")
                        # Update tab title to show manual setup required
                        try:
                            self.driver.execute_script(f"document.title = 'MFFUHijack - {account_type} (MANUAL)';")
                        except Exception:
                            pass


                except Exception as e:
                    self.log_message(f"Error processing {account_type}: {str(e)}", "error")
                    console.error(f"Error processing {account_type}: {str(e)}")

            # Switch back to first tab
            if self.driver.window_handles:
                self.driver.switch_to.window(self.driver.window_handles[0])

            # Print final status to system console
            total_tabs = len(self.preloaded_pages)
            console.success(f"Successfully processed {total_tabs} account types")

            if standard_types:
                console.success(f"Standard accounts automated: {', '.join(standard_types)}")
                console.info("Standard accounts are ready for instant code submission")

            if reset_types:
                console.info("MANUAL SETUP REQUIRED for Reset accounts:")
                console.info("1. Navigate to the stats page")
                console.info("2. Select your account from the dropdown")
                console.info("3. Click 'Reset Now' button")
                console.info("4. Click 'Add coupon code' button to make textbox visible")
                console.info("5. Leave the coupon code field empty and ready")

            console.success("All selected account types processed successfully")
            console.info("System ready for automatic code submission")

            # Remove GUI logging to eliminate delays
            return True

        except Exception as e:
            self.log_message(f"Error during account setup: {str(e)}", "error")
            return False

    def get_opened_tabs_info(self) -> Dict[str, str]:
        """Get information about opened tabs"""
        info = {}
        for account_type, window_handle in self.preloaded_pages.items():
            info[account_type] = f"Tab opened"
        return info

    def _navigate_to_checkout(self) -> bool:
        """Navigate through the standard checkout process for standard account types"""
        try:
            # Quick page load check - reduced timeout for speed
            WebDriverWait(self.driver, 2).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Click first Next button
            self.log_message("Looking for first Next button...")
            next_selectors = [
                "//button[contains(text(), 'Next')]",
                "//button[contains(text(), 'NEXT')]",
                "//input[@type='submit']",
                "//button[@type='submit']",
                "//button[contains(@class, 'next')]",
                "//button[contains(@class, 'submit')]"
            ]

            next_button = self._find_clickable_element(next_selectors, timeout=2)  # Further reduced timeout
            if not next_button:
                # Quick alternative approach - no logging to reduce overhead
                submit_selectors = [
                    "//button[@type='submit']",
                    "//input[@type='submit']",
                    "//button[contains(@class, 'submit')]"
                ]
                next_button = self._find_clickable_element(submit_selectors, timeout=1)

            if not next_button:
                raise Exception("No clickable Next/Submit button found")

            self._enhanced_click(next_button, "Next button")
            self.log_message("First Next button clicked")

            # Quick check for terms/conditions - no waiting
            checkboxes = self.driver.find_elements(By.XPATH, "//input[@type='checkbox']")
            if checkboxes:
                self.log_message("Found checkboxes, clicking them...")
                for checkbox in checkboxes:
                    try:
                        if not checkbox.is_selected() and checkbox.is_enabled():
                            self.driver.execute_script("arguments[0].click();", checkbox)
                    except Exception:
                        continue
                self.log_message("Checkboxes processed")
            else:
                self.log_message("No checkboxes found, proceeding...")

            # Click second Next button - optimized for speed
            next_button = self._find_clickable_element(next_selectors, timeout=2)  # Further reduced timeout
            if not next_button:
                raise Exception("Second Next button not found")

            self._enhanced_click(next_button, "Second Next button")

            # Wait for checkout page with faster detection
            checkout_selectors = [
                "//input[@placeholder='Coupon code']",
                "//input[contains(@placeholder, 'coupon')]",
                "//button[contains(text(), 'Add coupon')]",
                "//input[@placeholder='CVV']"
            ]

            # Quick checkout detection - no retries to avoid delays
            checkout_element = self._find_element(checkout_selectors, timeout=2)
            if not checkout_element:
                # Don't fail - proceed to coupon setup anyway
                pass

            # Optimized coupon setup - minimal waits for maximum speed
            coupon_button_selectors = [
                "//button[contains(text(), 'Add coupon code')]",
                "//button[contains(text(), 'Add coupon')]",
                "//button[contains(@class, 'coupon')]",
                "//a[contains(text(), 'Add coupon')]"
            ]

            coupon_textbox_selectors = [
                "//input[@placeholder='Coupon code']",
                "//input[contains(@placeholder, 'coupon')]",
                "//input[contains(@name, 'coupon')]"
            ]

            # Try to find coupon textbox first (might already be visible)
            coupon_textbox = self._find_element(coupon_textbox_selectors, timeout=0.5)

            if not coupon_textbox:
                # Try clicking coupon button if textbox not visible
                coupon_button = self._find_clickable_element(coupon_button_selectors, timeout=1)
                if coupon_button:
                    self._enhanced_click(coupon_button, "Add coupon code button")
                    # Quick check for textbox after clicking
                    coupon_textbox = self._find_element(coupon_textbox_selectors, timeout=1)

            if coupon_textbox:
                # Focus textbox and pre-fill CVV for instant submission
                self.driver.execute_script("arguments[0].focus();", coupon_textbox)
                self._prefill_cvv_if_available()

            # Always return True to avoid blocking - manual setup can handle missing elements
            return True

            self.log_message("Checkout automation completed - READY FOR INSTANT SUBMISSION")
            return True

        except Exception as e:
            self.log_message(f"Error navigating to checkout: {str(e)}", "error")
            return False

    def _prefill_cvv_if_available(self):
        """CVV prefill removed - manual entry required"""
        # CVV automation has been removed - users must manually enter CVV
        pass

    def _find_element(self, selectors: List[str], timeout: int = 5):
        """Find element using multiple selectors - shared timeout across all selectors"""
        import time
        start_time = time.time()

        while time.time() - start_time < timeout:
            for selector in selectors:
                try:
                    element = WebDriverWait(self.driver, 0.1).until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )
                    return element
                except TimeoutException:
                    continue
            time.sleep(0.1)  # Brief pause before trying again
        return None

    def _find_clickable_element(self, selectors: List[str], timeout: int = 5):
        """Find clickable element using multiple selectors - shared timeout across all selectors"""
        import time
        start_time = time.time()

        while time.time() - start_time < timeout:
            for selector in selectors:
                try:
                    element = WebDriverWait(self.driver, 0.1).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    return element
                except TimeoutException:
                    continue
            time.sleep(0.1)  # Brief pause before trying again
        return None

    def _enhanced_click(self, element, description: str = "element"):
        """Enhanced click with multiple strategies"""
        try:
            # Try regular click first
            element.click()
        except Exception:
            try:
                # Try JavaScript click
                self.driver.execute_script("arguments[0].click();", element)
            except Exception:
                # Try action chains
                from selenium.webdriver.common.action_chains import ActionChains
                ActionChains(self.driver).move_to_element(element).click().perform()

    def _fast_click(self, element):
        """Fast click using JavaScript"""
        self.driver.execute_script("arguments[0].click();", element)

    def _get_account_url(self, account_type: str) -> Optional[str]:
        """Get URL for account type with correct challenge URLs for preloading"""
        # Direct URL mapping for account types (not using MFF_URLS for challenge pages)
        account_urls = {
            "starter": "https://myfundedfutures.com/challenge?id=40&platform=Tradovate",
            "starter plus": "https://myfundedfutures.com/challenge?id=39&platform=Tradovate",
            "expert": "https://myfundedfutures.com/challenge?id=43&platform=Tradovate",
            "free reset code": "https://myfundedfutures.com/stats",  # Reset codes use stats page
            "reset": "https://myfundedfutures.com/stats"  # Alternative name for reset
        }

        account_key = account_type.lower()
        url = account_urls.get(account_key)

        if url:
            self.log_message(f"Mapped '{account_type}' to URL: {url}")
            return url
        else:
            self.log_message(f"No URL mapping found for account type: '{account_type}'", "error")
            self.log_message(f"Available account types: {list(account_urls.keys())}", "error")
            return None







    def _find_clickable_element(self, selectors: List[str], timeout: int = 2):
        """Find first clickable element from multiple selectors with optimized speed"""
        # Reduce default timeout from 5 to 2 seconds for faster detection
        for selector in selectors:
            try:
                element = WebDriverWait(self.driver, timeout).until(
                    EC.element_to_be_clickable((By.XPATH, selector))
                )
                # Remove verbose logging to reduce overhead
                return element
            except TimeoutException:
                continue
        return None

    def _find_element(self, selectors: List[str], timeout: int = 1):
        """Find first present element from multiple selectors with optimized speed"""
        # Reduce default timeout from 5 to 1 second for faster detection
        for selector in selectors:
            try:
                element = WebDriverWait(self.driver, timeout).until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )
                # Remove verbose logging to reduce overhead
                return element
            except TimeoutException:
                continue
        return None

    def _enhanced_click(self, element, element_name: str = "element"):
        """Enhanced clicking method with multiple retry strategies"""
        try:
            # Ensure element is in view
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
            # Removed delay for faster processing

            # Try multiple click strategies
            click_strategies = [
                lambda: element.click(),  # Standard click
                lambda: self.driver.execute_script("arguments[0].click();", element),  # JS click
                lambda: self.driver.execute_script("arguments[0].dispatchEvent(new MouseEvent('click', {bubbles: true}));", element),  # Dispatch event
            ]

            for i, strategy in enumerate(click_strategies):
                try:
                    strategy()
                    self.log_message(f"✅ {element_name} clicked successfully (method {i+1})")
                    return True
                except Exception as e:
                    if i == len(click_strategies) - 1:  # Last strategy
                        raise e
                    continue

        except Exception as e:
            self.log_message(f"❌ Failed to click {element_name}: {str(e)}", "error")
            return False

    def _fast_click(self, element):
        """Optimized clicking method with JavaScript fallback"""
        try:
            # Try standard click first
            element.click()
        except Exception:
            # Fallback to JavaScript click for stubborn elements
            self.driver.execute_script("arguments[0].click();", element)

    def _validate_checkout_ready(self) -> bool:
        """Pre-validate that checkout page has all required elements loaded"""
        try:
            self.log_message("🔍 Validating checkout page readiness...")

            # Check for essential elements
            required_selectors = [
                "//input[@placeholder='Coupon code'] | //button[contains(text(), 'Add coupon')]",
                "//input[@placeholder='CVV'] | //input[contains(@placeholder, 'cvv')]"
            ]

            for selector in required_selectors:
                try:
                    WebDriverWait(self.driver, 2).until(
                        EC.presence_of_element_located((By.XPATH, selector))
                    )
                except TimeoutException:
                    self.log_message(f"⚠️ Missing element: {selector}")
                    return False

            self.log_message("✅ Checkout page fully loaded and ready")
            return True

        except Exception as e:
            self.log_message(f"❌ Checkout validation error: {str(e)}", "error")
            return False

    def _ensure_element_ready(self, selectors: List[str], timeout: int = 3, element_name: str = "element"):
        """Ensure element is not only present but fully interactive"""
        element = self._find_element(selectors, timeout)
        if not element:
            raise Exception(f"{element_name} not found")

        # Wait for element to be fully interactive
        try:
            WebDriverWait(self.driver, 1).until(
                lambda driver: element.is_displayed() and element.is_enabled()
            )
        except TimeoutException:
            self.log_message(f"⚠️ {element_name} not fully ready, proceeding anyway")

        return element

    def submit_code(self, code: str, account_type: str, reset_account: Optional[str] = None) -> bool:
        """Submit a code for the specified account type - CVV must be manually entered"""
        # DEBUG: Log all parameters received
        self.log_message("🔍 SUBMIT_CODE DEBUG INFO:")
        self.log_message(f"  Code: '{code}'")
        self.log_message(f"  Account Type: '{account_type}'")
        self.log_message(f"  Reset Account: '{reset_account}' (for reset accounts)")
        self.log_message("⚠️ CVV MUST BE MANUALLY ENTERED - Automation removed")
        console.info(f"submit_code called - Code: {code}, Type: {account_type}")

        if not self.is_initialized:
            self.log_message("❌ Browser not initialized", "error")
            return False

        try:
            self.log_message(f"🎯 Submitting code '{code}' for {account_type}...")

            if account_type.lower() == "reset":
                self.log_message(f"🔄 Routing to reset code submission")
                return self._submit_reset_code(code, reset_account)
            else:
                self.log_message(f"🔄 Routing to standard code submission")
                return self._submit_standard_code(code, account_type)

        except Exception as e:
            self.log_message(f"❌ Error submitting code: {str(e)}", "error")
            self.log_message(traceback.format_exc(), "debug")
            return False

    def _submit_standard_code(self, code: str, account_type: str) -> bool:
        """Submit code for standard account types - CVV must be manually entered"""
        try:
            # Switch to the correct tab for the detected account type
            tab_found = False

            # Try exact match first
            if account_type in self.preloaded_pages:
                self.log_message(f"Switching to {account_type} tab...")
                self.driver.switch_to.window(self.preloaded_pages[account_type])
                tab_found = True
            else:
                # Try case-insensitive match
                for stored_type, window_handle in self.preloaded_pages.items():
                    if stored_type.lower() == account_type.lower():
                        self.log_message(f"Switching to {stored_type} tab (matched {account_type})...")
                        self.driver.switch_to.window(window_handle)
                        tab_found = True
                        break

            if not tab_found:
                self.log_message(f"No tab found for account type: {account_type}", "error")
                self.log_message(f"Available tabs: {list(self.preloaded_pages.keys())}", "error")
                return False

            # Verify we're on a checkout page
            try:
                WebDriverWait(self.driver, 3).until(
                    EC.presence_of_element_located((By.XPATH, "//input[@placeholder='Coupon code'] | //input[contains(@placeholder, 'coupon')]"))
                )
                self.log_message(f"Successfully switched to {account_type} checkout page")
            except TimeoutException:
                self.log_message(f"Tab for {account_type} may not be ready - ensure manual setup is complete", "error")
                return False

            # Find coupon textbox (should be visible from manual setup)
            self.log_message("Finding coupon code textbox...")
            coupon_textbox_selectors = [
                "//input[@placeholder='Coupon code']",
                "//input[contains(@placeholder, 'coupon')]",
                "//input[contains(@name, 'coupon')]"
            ]

            coupon_textbox = self._find_element(coupon_textbox_selectors, timeout=5)
            if not coupon_textbox:
                self.log_message("Coupon textbox not found - ensure manual setup is complete", "error")
                return False

            # Test code input with multiple methods
            self.log_message(f"🧪 Testing code input: {code}")
            console.info(f"Testing code input methods for: {code}")

            # Method 1: Simple send_keys approach
            self.log_message("🔧 Method 1: Testing simple send_keys...")
            try:
                # Focus and clear
                coupon_textbox.click()
                self.msleep(100)
                coupon_textbox.clear()
                self.msleep(100)

                # Send the code
                coupon_textbox.send_keys(code)
                self.msleep(200)

                # Verify
                simple_value = coupon_textbox.get_attribute("value")
                self.log_message(f"🔍 Method 1 result - Expected: '{code}', Got: '{simple_value}'")

                if simple_value == code:
                    self.log_message("✅ Code entered successfully with Method 1 (Simple)")
                    console.success(f"Code entered successfully: {code}")
                else:
                    # Method 2: Enhanced JavaScript approach
                    self.log_message("🔧 Method 2: Testing enhanced JavaScript...")

                    # Focus the field first
                    self.driver.execute_script("arguments[0].focus();", coupon_textbox)

                    # Set the actual value with comprehensive event handling
                    self.driver.execute_script("""
                        var field = arguments[0];
                        var value = arguments[1];

                        // Clear field completely
                        field.value = '';

                        // Set the new value (actual text, not placeholder)
                        field.value = value;

                        // Trigger all necessary events for modern web frameworks
                        field.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
                        field.dispatchEvent(new Event('change', { bubbles: true, cancelable: true }));
                        field.dispatchEvent(new Event('keyup', { bubbles: true, cancelable: true }));
                        field.dispatchEvent(new Event('keydown', { bubbles: true, cancelable: true }));
                        field.dispatchEvent(new Event('blur', { bubbles: true, cancelable: true }));

                        // Force update for React/Vue/Angular components
                        if (field._valueTracker) {
                            field._valueTracker.setValue('');
                            field._valueTracker.setValue(value);
                        }

                        // Additional React handling
                        var nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
                        if (nativeInputValueSetter) {
                            nativeInputValueSetter.call(field, value);
                            field.dispatchEvent(new Event('input', { bubbles: true }));
                        }
                    """, coupon_textbox, code)

                    # Wait for events to process
                    self.msleep(100)

                    # Verify code was entered correctly
                    js_value = self.driver.execute_script("return arguments[0].value;", coupon_textbox)
                    self.log_message(f"🔍 Method 2 result - Expected: '{code}', Got: '{js_value}'")

                    if js_value == code:
                        self.log_message("✅ Code entered successfully with Method 2 (JavaScript)")
                        console.success(f"Code entered successfully: {code}")
                    else:
                        self.log_message(f"❌ Both methods failed. Expected: {code}, Got: {js_value or 'EMPTY'}")
                        console.error("Code entry failed with both methods")
                        return False

            except Exception as e:
                self.log_message(f"❌ Failed to enter code: {e}", "error")
                console.error(f"Failed to enter code: {e}")
                return False

            # Click APPLY button
            self.log_message("Clicking APPLY button...")
            apply_selectors = [
                "//button[contains(text(), 'APPLY')]",
                "//button[contains(text(), 'Apply')]",
                "//input[@type='submit' and contains(@value, 'APPLY')]",
                "//button[contains(@class, 'apply')]"
            ]

            apply_button = self._find_clickable_element(apply_selectors, timeout=5)
            if not apply_button:
                self.log_message("APPLY button not found", "error")
                return False

            self._fast_click(apply_button)
            self.log_message("APPLY button clicked")

            # Quick error check
            try:
                error_selectors = [
                    "//div[contains(@class, 'error')]",
                    "//div[contains(@class, 'alert')]",
                    "//span[contains(@class, 'error')]",
                    "//div[contains(text(), 'invalid') or contains(text(), 'expired')]"
                ]

                for selector in error_selectors:
                    try:
                        error_element = WebDriverWait(self.driver, 1).until(
                            EC.presence_of_element_located((By.XPATH, selector))
                        )
                        if error_element.is_displayed():
                            error_text = error_element.text
                            self.log_message(f"❌ Code error: {error_text}", "error")
                            if self.signals and PYQT_AVAILABLE:
                                self.signals.code_submitted.emit(False, error_text)
                            return False
                    except TimeoutException:
                        continue

                self.log_message("✅ Code accepted - no errors detected")

            except Exception:
                self.log_message("✅ Code accepted (error check skipped for speed)")

            # Manual CVV Entry Instructions
            self.log_message("⚠️ MANUAL CVV ENTRY REQUIRED")
            self.log_message("🔧 CVV automation has been removed - you must manually enter CVV")
            console.warning("CVV automation removed - manual entry required")

            # Focus on the coupon code field to help user locate it
            try:
                self.driver.execute_script("arguments[0].scrollIntoView(true);", coupon_textbox)
                self.driver.execute_script("arguments[0].focus();", coupon_textbox)
                self.log_message("✅ Coupon code field focused for your reference")
                console.success("Coupon code field focused - ready for manual CVV entry")
            except Exception:
                pass

            # Instructions for manual CVV entry
            self.log_message("📋 MANUAL SETUP INSTRUCTIONS:")
            self.log_message("1. Manually enter your CVV in the CVV field on this page")
            self.log_message("2. Do NOT click submit yet")
            self.log_message("3. Focus back on the coupon code field when done")
            self.log_message("4. The system will automatically submit after code entry")
            console.info("Manual CVV entry required - see instructions above")

            # Wait a moment for user to see the instructions
            self.msleep(2000)  # 2 second pause for user to read instructions

            # No automatic submission - just return success for code entry
            self.log_message("✅ Code entry completed - manual CVV entry required before submission")
            console.success(f"Code {code} entered successfully for {account_type} - manual CVV entry required")

            if self.signals and PYQT_AVAILABLE:
                self.signals.code_submitted.emit(True, f"Code {code} entered for {account_type} - manual CVV entry required")
            return True

        except Exception as e:
            self.log_message(f"❌ Error submitting standard code: {str(e)}", "error")
            return False

        except Exception as e:
            self.log_message(f"❌ Error submitting standard code: {str(e)}", "error")
            return False

    def _submit_reset_code(self, code: str, reset_account: str) -> bool:
        """Submit code for reset account type - assumes manual setup is complete"""
        try:
            if not reset_account:
                self.log_message("Reset account number not provided", "error")
                return False

            # Switch to the reset account tab (should be manually set up)
            tab_found = False
            reset_account_types = ["free reset code", "reset"]

            for reset_type in reset_account_types:
                if reset_type in self.preloaded_pages:
                    self.log_message(f"Switching to {reset_type} tab...")
                    self.driver.switch_to.window(self.preloaded_pages[reset_type])
                    tab_found = True
                    break

            # Try case-insensitive match
            if not tab_found:
                for stored_type, window_handle in self.preloaded_pages.items():
                    if stored_type.lower() in reset_account_types:
                        self.log_message(f"Switching to {stored_type} tab...")
                        self.driver.switch_to.window(window_handle)
                        tab_found = True
                        break

            if not tab_found:
                self.log_message("No reset account tab found", "error")
                self.log_message(f"Available tabs: {list(self.preloaded_pages.keys())}", "error")
                return False

            # Verify we're on the reset page with coupon textbox ready
            try:
                WebDriverWait(self.driver, 3).until(
                    EC.presence_of_element_located((By.XPATH, "//input[@placeholder='Coupon code'] | //input[contains(@placeholder, 'coupon')]"))
                )
                self.log_message("Reset page ready for code input")
            except TimeoutException:
                self.log_message("Reset page not ready - ensure manual setup is complete", "error")
                return False

            # Find coupon textbox (should be visible from manual setup)
            self.log_message("Finding coupon code textbox...")
            coupon_textbox_selectors = [
                "//input[@placeholder='Coupon code']",
                "//input[contains(@placeholder, 'coupon')]",
                "//input[contains(@name, 'coupon')]"
            ]

            coupon_textbox = self._find_element(coupon_textbox_selectors, timeout=5)
            if not coupon_textbox:
                self.log_message("Coupon textbox not found - ensure manual setup is complete", "error")
                return False

            # Enter reset coupon code with proper value setting (not placeholder)
            self.log_message(f"Entering reset code: {code}")
            console.info(f"Setting reset code value: {code}")

            try:
                # Method 1: Proper JavaScript value setting with event handling
                self.log_message("🔧 Setting reset code value with enhanced JavaScript...")

                # Focus the field first
                self.driver.execute_script("arguments[0].focus();", coupon_textbox)

                # Set the actual value with comprehensive event handling
                self.driver.execute_script("""
                    var field = arguments[0];
                    var value = arguments[1];

                    // Clear field completely
                    field.value = '';

                    // Set the new value (actual text, not placeholder)
                    field.value = value;

                    // Trigger all necessary events for modern web frameworks
                    field.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
                    field.dispatchEvent(new Event('change', { bubbles: true, cancelable: true }));
                    field.dispatchEvent(new Event('keyup', { bubbles: true, cancelable: true }));
                    field.dispatchEvent(new Event('keydown', { bubbles: true, cancelable: true }));
                    field.dispatchEvent(new Event('blur', { bubbles: true, cancelable: true }));

                    // Force update for React/Vue/Angular components
                    if (field._valueTracker) {
                        field._valueTracker.setValue('');
                        field._valueTracker.setValue(value);
                    }

                    // Additional React handling
                    var nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
                    if (nativeInputValueSetter) {
                        nativeInputValueSetter.call(field, value);
                        field.dispatchEvent(new Event('input', { bubbles: true }));
                    }
                """, coupon_textbox, code)

                # Wait for events to process
                self.msleep(100)

                # Verify reset code was entered correctly
                entered_value = self.driver.execute_script("return arguments[0].value;", coupon_textbox)
                if entered_value == code:
                    self.log_message("✅ Reset code entered and verified successfully")
                    console.success(f"Reset code entered successfully: {code}")
                else:
                    # Method 2: Try SendKeys as fallback
                    self.log_message("⚠️ JavaScript method failed, trying SendKeys...")

                    # Clear field
                    coupon_textbox.clear()

                    # Send keys directly
                    coupon_textbox.send_keys(code)

                    # Trigger events after SendKeys
                    self.driver.execute_script("""
                        var field = arguments[0];
                        field.dispatchEvent(new Event('input', { bubbles: true }));
                        field.dispatchEvent(new Event('change', { bubbles: true }));
                    """, coupon_textbox)

                    # Final verification
                    final_value = self.driver.execute_script("return arguments[0].value;", coupon_textbox)
                    if final_value == code:
                        self.log_message("✅ Reset code entered successfully with SendKeys")
                        console.success(f"Reset code entered with SendKeys: {code}")
                    else:
                        self.log_message(f"❌ Both methods failed. Expected: {code}, Got: {final_value or 'EMPTY'}")
                        console.error("Reset code entry failed with both methods")
                        return False

            except Exception as e:
                self.log_message(f"❌ Failed to enter reset code: {e}", "error")
                console.error(f"Failed to enter reset code: {e}")
                return False

            # Apply code
            self.log_message("Clicking APPLY button...")
            apply_selectors = [
                "//button[contains(text(), 'APPLY')]",
                "//button[contains(text(), 'Apply')]",
                "//input[@type='submit' and contains(@value, 'APPLY')]"
            ]

            apply_button = self._find_clickable_element(apply_selectors, timeout=5)
            if not apply_button:
                self.log_message("APPLY button not found", "error")
                return False

            self._fast_click(apply_button)
            self.log_message("APPLY button clicked")

            # Manual CVV Entry Required for Reset
            self.log_message("⚠️ MANUAL CVV ENTRY REQUIRED FOR RESET")
            self.log_message("🔧 CVV automation has been removed - you must manually enter CVV")
            console.warning("CVV automation removed - manual entry required for reset")

            # Focus on the coupon code field to help user locate it
            try:
                self.driver.execute_script("arguments[0].scrollIntoView(true);", coupon_textbox)
                self.driver.execute_script("arguments[0].focus();", coupon_textbox)
                self.log_message("✅ Coupon code field focused for your reference")
                console.success("Coupon code field focused - ready for manual CVV entry")
            except Exception:
                pass

            # Instructions for manual CVV entry
            self.log_message("📋 MANUAL RESET SETUP INSTRUCTIONS:")
            self.log_message("1. Manually enter your CVV in the CVV field on this page")
            self.log_message("2. Do NOT click submit yet")
            self.log_message("3. Focus back on the coupon code field when done")
            self.log_message("4. The system will automatically submit after code entry")
            console.info("Manual CVV entry required for reset - see instructions above")

            # Wait a moment for user to see the instructions
            self.msleep(2000)  # 2 second pause for user to read instructions

            # No automatic submission - just return success for code entry
            self.log_message("✅ Reset code entry completed - manual CVV entry required before submission")
            console.success(f"Reset code {code} entered successfully - manual CVV entry required")


            if self.signals and PYQT_AVAILABLE:
                self.signals.code_submitted.emit(True, f"Reset code {code} entered for account {reset_account} - manual CVV entry required")
            return True

        except Exception as e:
            self.log_message(f"Error submitting reset code: {str(e)}", "error")
            return False


# Global browser automation instance
browser_automation = BrowserAutomation()
